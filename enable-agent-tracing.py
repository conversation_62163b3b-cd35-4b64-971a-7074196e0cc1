#!/usr/bin/env python3
"""
Script to enable agent-level LangSmith tracing for DAP debugging.
This script sets the DEBUG environment variable to enable extended logging.
"""

import os
import subprocess
from pathlib import Path

def enable_debug_mode():
    """Enable DEBUG mode in the duo workflow service environment."""
    print("🔧 Enabling DEBUG mode for agent-level tracing...")
    
    # Check if we're in the right directory
    if not Path("duo-workflow-executor").exists():
        print("❌ Error: Please run this script from the GDK root directory")
        return False

    # Update the .env file for duo workflow service
    env_file = Path("duo-workflow-executor/.env")
    
    # Read existing content
    env_content = ""
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_content = f.read()
    
    # Check if DEBUG is already set
    if "DEBUG=" in env_content:
        # Replace existing DEBUG setting
        lines = env_content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith("DEBUG="):
                lines[i] = "DEBUG=true"
                break
        env_content = '\n'.join(lines)
    else:
        # Add DEBUG setting
        env_content += "\n# Enable agent-level tracing\nDEBUG=true\n"
    
    # Write back to file
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print(f"✅ Updated {env_file} with DEBUG=true")
    
    # Also update the main example.env if it exists
    example_env = Path("example.env")
    if example_env.exists():
        with open(example_env, 'r') as f:
            content = f.read()
        
        if "DEBUG=false" in content:
            content = content.replace("DEBUG=false", "DEBUG=true")
            with open(example_env, 'w') as f:
                f.write(content)
            print(f"✅ Updated {example_env} with DEBUG=true")
    
    return True

def restart_services():
    """Restart the duo workflow service to pick up the new configuration."""
    print("🔄 Restarting duo workflow service...")
    
    try:
        # Stop the service
        subprocess.run(["gdk", "stop", "duo-workflow-service"], check=False)
        
        # Start the service
        result = subprocess.run(["gdk", "start", "duo-workflow-service"], check=True)
        
        print("✅ Duo workflow service restarted successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error restarting service: {e}")
        return False

def main():
    print("🚀 Enabling Agent-Level LangSmith Tracing for DAP")
    print("=" * 50)
    
    if not enable_debug_mode():
        return
    
    print("\n📋 Next steps:")
    print("1. Restart the duo workflow service:")
    print("   gdk stop duo-workflow-service")
    print("   gdk start duo-workflow-service")
    print("\n2. Test a DAP workflow from VS Code or GitLab")
    print("\n3. Check LangSmith traces at: https://smith.langchain.com")
    print("   Project: rathid-gdk")
    print("\n🔍 You should now see agent-specific traces like:")
    print("   - Goal_Disambiguation_Agent")
    print("   - Context_Gathering_Agent") 
    print("   - Planning_Agent")
    print("   - Execution_Agent")
    print("\n📝 To disable agent tracing later, set DEBUG=false and restart the service")

if __name__ == "__main__":
    main()
