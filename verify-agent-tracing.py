#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify that agent-level LangSmith tracing is working.
This script checks the configuration and provides debugging information.
"""

import os
import json
from pathlib import Path

def check_debug_setting():
    """Check if DEBUG is enabled in the duo workflow service."""
    print("🔍 Checking DEBUG configuration...")
    
    env_file = Path("duo-workflow-executor/.env")
    if not env_file.exists():
        print("❌ duo-workflow-executor/.env file not found")
        return False
    
    with open(env_file, 'r') as f:
        content = f.read()
    
    if "DEBUG=true" in content:
        print("✅ DEBUG=true found in duo-workflow-executor/.env")
        return True
    elif "DEBUG=false" in content:
        print("❌ DEBUG=false found in duo-workflow-executor/.env")
        print("   Run: python3 enable-agent-tracing.py")
        return False
    else:
        print("⚠️  DEBUG setting not found in duo-workflow-executor/.env")
        print("   Run: python3 enable-agent-tracing.py")
        return False

def check_langsmith_config():
    """Check LangSmith configuration."""
    print("\n🔍 Checking LangSmith configuration...")
    
    langsmith_env = Path("langsmith.env")
    if langsmith_env.exists():
        with open(langsmith_env, 'r') as f:
            content = f.read()
        
        if 'LANGSMITH_TRACING="true"' in content or "LANGSMITH_TRACING=true" in content:
            print("✅ LANGSMITH_TRACING=true found")
        else:
            print("❌ LANGSMITH_TRACING not enabled")
        
        if "rathid-gdk" in content:
            print("✅ LangSmith project 'rathid-gdk' configured")
        else:
            print("❌ LangSmith project not configured")
    else:
        print("❌ langsmith.env file not found")

def check_service_status():
    """Check if the duo workflow service is running."""
    print("\n🔍 Checking service status...")
    
    try:
        import subprocess
        result = subprocess.run(
            ["gdk", "status", "duo-workflow-service"],
            capture_output=True,
            text=True
        )

        if result.returncode == 0 and ("run" in result.stdout or "up" in result.stdout):
            print("✅ duo-workflow-service is running")
            return True
        else:
            print("❌ duo-workflow-service is not running")
            print("   Run: gdk start duo-workflow-service")
            return False
    except Exception as e:
        print(f"❌ Error checking service status: {e}")
        return False

def main():
    print("🔍 Agent-Level LangSmith Tracing Verification")
    print("=" * 50)
    
    debug_ok = check_debug_setting()
    check_langsmith_config()
    service_ok = check_service_status()
    
    print("\n📋 Summary:")
    if debug_ok and service_ok:
        print("✅ Configuration looks good!")
        print("\n🚀 Next steps:")
        print("1. Test a DAP workflow from VS Code or GitLab")
        print("2. Check LangSmith traces at: https://smith.langchain.com")
        print("3. Look for project: rathid-gdk")
        print("4. Verify you see agent-specific traces like:")
        print("   - Goal_Disambiguation_Agent")
        print("   - Context_Gathering_Agent")
        print("   - Planning_Agent") 
        print("   - Execution_Agent")
    else:
        print("❌ Configuration issues found")
        print("\n🔧 To fix:")
        if not debug_ok:
            print("   - Run: python3 enable-agent-tracing.py")
        if not service_ok:
            print("   - Run: gdk start duo-workflow-service")

if __name__ == "__main__":
    main()
